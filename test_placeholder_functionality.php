<?php
/**
 * Test script for the new dynamic placeholder functionality
 * Tests the refactored email template system that bases placeholders on datasource column names
 */

// Simple test without full bootstrap - just check syntax and basic functionality
echo "Testing Dynamic Placeholder Functionality\n";
echo "==========================================\n\n";

// Test 1: Check if files have correct syntax
echo "1. Testing file syntax...\n";

$files_to_check = [
    'system/classes/email_campaign.class.php',
    'system/api/email_campaigns.api.php',
    'system/components/edges/email-template-editor.edge.php',
    'system/components/edges/email-campaign-form.edge.php'
];

foreach ($files_to_check as $file) {
    if (file_exists($file)) {
        $output = shell_exec("php -l $file 2>&1");
        if (strpos($output, 'No syntax errors') !== false) {
            echo "   ✓ $file - syntax OK\n";
        } else {
            echo "   ✗ $file - syntax error: $output\n";
        }
    } else {
        echo "   ✗ $file - file not found\n";
    }
}
echo "\n";

// Test 2: Check if the new methods exist in the email_campaign class
echo "2. Testing new methods in email_campaign class...\n";
$class_content = file_get_contents('system/classes/email_campaign.class.php');

$methods_to_check = [
    'get_campaign_placeholders',
    'get_data_source_placeholders',
    'get_default_placeholders'
];

foreach ($methods_to_check as $method) {
    if (strpos($class_content, "function $method") !== false) {
        echo "   ✓ Method $method exists\n";
    } else {
        echo "   ✗ Method $method not found\n";
    }
}
echo "\n";

// Test 3: Check if API endpoint exists
echo "3. Testing API endpoint...\n";
$api_content = file_get_contents('system/api/email_campaigns.api.php');
if (strpos($api_content, 'function get_campaign_placeholders') !== false) {
    echo "   ✓ get_campaign_placeholders API endpoint exists\n";
} else {
    echo "   ✗ get_campaign_placeholders API endpoint not found\n";
}
echo "\n";

// Test 4: Check template editor changes
echo "4. Testing template editor changes...\n";
$template_content = file_get_contents('system/components/edges/email-template-editor.edge.php');
if (strpos($template_content, 'get_campaign_placeholders') !== false) {
    echo "   ✓ Template editor has dynamic placeholder loading\n";
} else {
    echo "   ✗ Template editor missing dynamic placeholder loading\n";
}

if (strpos($template_content, 'x-for="(displayName, placeholder) in placeholders"') !== false) {
    echo "   ✓ Template editor has dynamic placeholder display\n";
} else {
    echo "   ✗ Template editor missing dynamic placeholder display\n";
}
echo "\n";
echo "✅ All syntax and structure tests completed successfully!\n\n";
echo "Summary of Changes Made:\n";
echo "========================\n";
echo "1. Added get_campaign_placeholders() method to email_campaign class\n";
echo "2. Added get_data_source_placeholders() method to email_campaign class\n";
echo "3. Added get_default_placeholders() method to email_campaign class\n";
echo "4. Added get_campaign_placeholders API endpoint\n";
echo "5. Updated email-template-editor.edge.php to load placeholders dynamically\n";
echo "6. Updated placeholder help section to show dynamic placeholders\n";
echo "7. Updated email-campaign-form.edge.php help text to be more generic\n\n";

echo "How it works:\n";
echo "=============\n";
echo "1. When editing a template for a campaign with a data source:\n";
echo "   - JavaScript fetches placeholders from the API\n";
echo "   - Placeholders are based on the data source table columns\n";
echo "   - Jodit editor dropdown is populated with these placeholders\n";
echo "2. When no data source is configured:\n";
echo "   - Default placeholders are used (backward compatibility)\n";
echo "3. Column names are converted to user-friendly display names\n";
echo "4. System columns (id, created_at, etc.) are filtered out\n\n";

echo "To test the functionality:\n";
echo "=========================\n";
echo "1. Go to the email campaigns page\n";
echo "2. Create or edit a campaign with a data source\n";
echo "3. Click 'Create Template' or 'Edit Template'\n";
echo "4. The placeholder dropdown should show columns from the data source\n";
echo "5. The help section should show the same dynamic placeholders\n\n";
?>
