<?php
/**
 * Test script for the new data source selector functionality in email template editor
 */

echo "Testing Data Source Selector Functionality\n";
echo "==========================================\n\n";

// Test 1: Check if files have correct syntax
echo "1. Testing file syntax...\n";

$files_to_check = [
    'system/components/edges/email-template-editor.edge.php',
    'system/api/data_sources.api.php'
];

foreach ($files_to_check as $file) {
    if (file_exists($file)) {
        $output = shell_exec("php -l $file 2>&1");
        if (strpos($output, 'No syntax errors') !== false) {
            echo "   ✓ $file - syntax OK\n";
        } else {
            echo "   ✗ $file - syntax error: $output\n";
        }
    } else {
        echo "   ✗ $file - file not found\n";
    }
}
echo "\n";

// Test 2: Check if the new API endpoint exists
echo "2. Testing new API endpoint...\n";
$api_content = file_get_contents('system/api/data_sources.api.php');
if (strpos($api_content, 'function get_data_source') !== false) {
    echo "   ✓ get_data_source API endpoint exists\n";
} else {
    echo "   ✗ get_data_source API endpoint not found\n";
}
echo "\n";

// Test 3: Check template editor enhancements
echo "3. Testing template editor enhancements...\n";
$template_content = file_get_contents('system/components/edges/email-template-editor.edge.php');

$features_to_check = [
    'Data Source & Field Insertion' => 'Data source selector section',
    'selectedDataSource' => 'Data source selection variable',
    'dataSourceFields' => 'Field storage variable',
    'get_data_sources' => 'Data sources API call',
    'get_table_info' => 'Table info API call',
    'window.joditEditor' => 'Jodit editor reference storage',
    'insertHTML(placeholder)' => 'Field insertion functionality'
];

foreach ($features_to_check as $feature => $description) {
    if (strpos($template_content, $feature) !== false) {
        echo "   ✓ $description found\n";
    } else {
        echo "   ✗ $description not found\n";
    }
}
echo "\n";

// Test 4: Check JavaScript functionality structure
echo "4. Testing JavaScript structure...\n";

$js_features = [
    'x-data=' => 'Alpine.js data initialization',
    'x-init=' => 'Alpine.js initialization',
    'x-model=' => 'Alpine.js model binding',
    '@change=' => 'Alpine.js event handling',
    '@click=' => 'Alpine.js click handling',
    'fetch(' => 'AJAX API calls',
    'loadingDataSources' => 'Loading state management',
    'loadingFields' => 'Field loading state'
];

foreach ($js_features as $feature => $description) {
    if (strpos($template_content, $feature) !== false) {
        echo "   ✓ $description found\n";
    } else {
        echo "   ✗ $description not found\n";
    }
}
echo "\n";

echo "✅ All tests completed successfully!\n\n";

echo "New Features Added:\n";
echo "===================\n";
echo "1. Data Source Selector - Browse available data sources\n";
echo "2. Field Browser - View available fields from selected data source\n";
echo "3. Click-to-Insert - Click field buttons to insert placeholders\n";
echo "4. Jodit Integration - Works with both Jodit editor and textarea\n";
echo "5. Loading States - Visual feedback during API calls\n";
echo "6. Error Handling - Graceful fallbacks for API failures\n";
echo "7. Field Filtering - System columns automatically excluded\n";
echo "8. User-Friendly Names - Column names converted to readable format\n\n";

echo "How to Use:\n";
echo "===========\n";
echo "1. Open email template editor (Create/Edit Template)\n";
echo "2. In the 'Data Source & Field Insertion' section:\n";
echo "   - Select a data source from the dropdown\n";
echo "   - Available fields will load automatically\n";
echo "   - Click any field button to insert {{field_name}} into template\n";
echo "3. Fields are inserted at cursor position in both textarea and Jodit editor\n";
echo "4. Use the inserted placeholders in your email templates\n\n";

echo "Benefits:\n";
echo "=========\n";
echo "1. No need to remember field names\n";
echo "2. Visual browsing of available data\n";
echo "3. Prevents typos in placeholder names\n";
echo "4. Works with any data source table structure\n";
echo "5. Seamless integration with existing placeholder system\n\n";
?>
