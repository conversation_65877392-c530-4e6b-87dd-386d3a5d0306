<?php
/**
 * Test script to verify the data source fields fix
 */

echo "Testing Data Source Fields Fix\n";
echo "==============================\n\n";

// Test 1: Check if files have correct syntax
echo "1. Testing file syntax...\n";
$output = shell_exec("php -l system/components/edges/data-source-field-browser.edge.php 2>&1");
if (strpos($output, 'No syntax errors') !== false) {
    echo "   ✓ data-source-field-browser.edge.php - syntax OK\n";
} else {
    echo "   ✗ data-source-field-browser.edge.php - syntax error: $output\n";
}
echo "\n";

// Test 2: Check if selected_columns handling is implemented
echo "2. Testing selected_columns handling...\n";
$component_content = file_get_contents('system/components/edges/data-source-field-browser.edge.php');

$selected_columns_features = [
    'selected_columns' => 'Selected columns configuration check',
    'is_array($data_source[\'selected_columns\'])' => 'Array validation for selected columns',
    'foreach ($data_source[\'selected_columns\'] as $table => $columns)' => 'Table iteration for selected columns',
    'foreach ($columns as $column_name)' => 'Column iteration within tables',
    '\'table\' => $table' => 'Table information stored with field'
];

foreach ($selected_columns_features as $feature => $description) {
    if (strpos($component_content, $feature) !== false) {
        echo "   ✓ $description found\n";
    } else {
        echo "   ✗ $description not found\n";
    }
}
echo "\n";

// Test 3: Check if fallback to table info is still available
echo "3. Testing fallback mechanism...\n";
$fallback_features = [
    'Fallback to table info if no selected columns configured' => 'Fallback comment',
    'get_table_info($data_source[\'table_name\'])' => 'Table info fallback call',
    'if ($table_info && isset($table_info[\'columns\']))' => 'Table info validation'
];

foreach ($fallback_features as $feature => $description) {
    if (strpos($component_content, $feature) !== false) {
        echo "   ✓ $description found\n";
    } else {
        echo "   ✗ $description not found\n";
    }
}
echo "\n";

// Test 4: Check if table information is displayed
echo "4. Testing table information display...\n";
$display_features = [
    'isset($field[\'table\'])' => 'Table field check',
    'count($fields) > 10' => 'Conditional table display for many fields',
    '$field[\'table\']' => 'Table name display'
];

foreach ($display_features as $feature => $description) {
    if (strpos($component_content, $feature) !== false) {
        echo "   ✓ $description found\n";
    } else {
        echo "   ✗ $description not found\n";
    }
}
echo "\n";

echo "✅ Data source fields fix verification completed!\n\n";

echo "What was fixed:\n";
echo "===============\n";
echo "❌ BEFORE: Component tried to get all columns from main table only\n";
echo "✅ AFTER: Component uses selected_columns configuration from data source\n\n";

echo "Your data source configuration:\n";
echo "===============================\n";
echo "Data Source: Autodesk_autorenew\n";
echo "Main Table: autodesk_subscriptions\n";
echo "Joined Table: autodesk_accounts\n\n";

echo "Selected Columns (from your database dump):\n";
echo "autodesk_subscriptions:\n";
echo "  - subscriptionId\n";
echo "  - subscriptionReferenceNumber\n";
echo "  - quantity\n";
echo "  - status\n";
echo "  - startDate\n";
echo "  - endDate\n";
echo "  - paymentMethod\n";
echo "  - offeringId\n";
echo "  - offeringCode\n";
echo "  - offeringName\n";
echo "  - autoRenew\n\n";

echo "autodesk_accounts:\n";
echo "  - name\n";
echo "  - first_name\n";
echo "  - last_name\n";
echo "  - email\n";
echo "  - city\n";
echo "  - postal_code\n\n";

echo "Expected behavior now:\n";
echo "======================\n";
echo "1. Open email template editor\n";
echo "2. Select 'Autodesk_autorenew (autodesk_subscriptions)' from dropdown\n";
echo "3. Should see 17 fields total (11 from autodesk_subscriptions + 6 from autodesk_accounts)\n";
echo "4. Fields should show table names in parentheses since there are >10 fields\n";
echo "5. Click any field button to insert placeholder like {{subscriptionId}}\n\n";

echo "Field types will be inferred from names:\n";
echo "- subscriptionId, quantity, offeringId → number (green)\n";
echo "- startDate, endDate → date (purple)\n";
echo "- All others → text (blue)\n\n";
?>
