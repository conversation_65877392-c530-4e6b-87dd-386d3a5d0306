<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Data Source API Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .result { background: #f5f5f5; padding: 10px; margin: 10px 0; border-radius: 3px; }
        .error { background: #ffebee; color: #c62828; }
        .success { background: #e8f5e8; color: #2e7d32; }
        button { padding: 8px 16px; margin: 5px; cursor: pointer; }
    </style>
</head>
<body>
    <h1>Data Source API Test</h1>
    <p>This page tests the data source API endpoints to debug the empty dropdown issue.</p>

    <div class="test-section">
        <h2>Test 1: Get Data Sources</h2>
        <button onclick="testGetDataSources()">Test get_data_sources</button>
        <div id="dataSources" class="result"></div>
    </div>

    <div class="test-section">
        <h2>Test 2: Get Tables</h2>
        <button onclick="testGetTables()">Test get_tables</button>
        <div id="tables" class="result"></div>
    </div>

    <div class="test-section">
        <h2>Test 3: Get Table Info</h2>
        <input type="text" id="tableName" placeholder="Enter table name" value="autobooks_users">
        <button onclick="testGetTableInfo()">Test get_table_info</button>
        <div id="tableInfo" class="result"></div>
    </div>

    <script>
        const APP_ROOT = window.location.pathname.replace('/test_data_source_api.html', '');
        
        function testGetDataSources() {
            const resultDiv = document.getElementById('dataSources');
            resultDiv.innerHTML = 'Loading...';
            
            fetch(APP_ROOT + '/api/data_sources/get_data_sources', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                }
            })
            .then(response => {
                console.log('Data sources response status:', response.status);
                return response.json();
            })
            .then(data => {
                console.log('Data sources response:', data);
                resultDiv.className = 'result ' + (data.success ? 'success' : 'error');
                resultDiv.innerHTML = '<h3>Response:</h3><pre>' + JSON.stringify(data, null, 2) + '</pre>';
            })
            .catch(error => {
                console.error('Data sources error:', error);
                resultDiv.className = 'result error';
                resultDiv.innerHTML = '<h3>Error:</h3><pre>' + error.message + '</pre>';
            });
        }

        function testGetTables() {
            const resultDiv = document.getElementById('tables');
            resultDiv.innerHTML = 'Loading...';
            
            fetch(APP_ROOT + '/api/data_sources/get_tables', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                }
            })
            .then(response => {
                console.log('Tables response status:', response.status);
                return response.json();
            })
            .then(data => {
                console.log('Tables response:', data);
                resultDiv.className = 'result ' + (data.success ? 'success' : 'error');
                resultDiv.innerHTML = '<h3>Response:</h3><pre>' + JSON.stringify(data, null, 2) + '</pre>';
            })
            .catch(error => {
                console.error('Tables error:', error);
                resultDiv.className = 'result error';
                resultDiv.innerHTML = '<h3>Error:</h3><pre>' + error.message + '</pre>';
            });
        }

        function testGetTableInfo() {
            const tableName = document.getElementById('tableName').value;
            const resultDiv = document.getElementById('tableInfo');
            
            if (!tableName) {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = 'Please enter a table name';
                return;
            }
            
            resultDiv.innerHTML = 'Loading...';
            
            fetch(APP_ROOT + '/api/data_sources/get_table_info', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: 'table_name=' + encodeURIComponent(tableName)
            })
            .then(response => {
                console.log('Table info response status:', response.status);
                return response.json();
            })
            .then(data => {
                console.log('Table info response:', data);
                resultDiv.className = 'result ' + (data.success ? 'success' : 'error');
                resultDiv.innerHTML = '<h3>Response:</h3><pre>' + JSON.stringify(data, null, 2) + '</pre>';
            })
            .catch(error => {
                console.error('Table info error:', error);
                resultDiv.className = 'result error';
                resultDiv.innerHTML = '<h3>Error:</h3><pre>' + error.message + '</pre>';
            });
        }

        // Auto-run tests on page load
        window.onload = function() {
            console.log('APP_ROOT detected as:', APP_ROOT);
            console.log('Running automatic tests...');
            testGetDataSources();
            setTimeout(testGetTables, 1000);
        };
    </script>
</body>
</html>
