<?php
/**
 * Test script to verify the server-side implementation
 */

echo "Testing Server-Side Implementation\n";
echo "==================================\n\n";

// Test 1: Check if files have correct syntax
echo "1. Testing file syntax...\n";
$files_to_check = [
    'system/components/edges/email-template-editor.edge.php',
    'system/components/edges/data-source-field-browser.edge.php',
    'system/api/email_campaigns.api.php'
];

foreach ($files_to_check as $file) {
    if (file_exists($file)) {
        $output = shell_exec("php -l $file 2>&1");
        if (strpos($output, 'No syntax errors') !== false) {
            echo "   ✓ $file - syntax OK\n";
        } else {
            echo "   ✗ $file - syntax error: $output\n";
        }
    } else {
        echo "   ✗ $file - file not found\n";
    }
}
echo "\n";

// Test 2: Check if new Edge component exists
echo "2. Testing new Edge component...\n";
$field_browser_content = file_get_contents('system/components/edges/data-source-field-browser.edge.php');

$component_features = [
    '@props([' => 'Component props definition',
    'use system\data_source_manager' => 'Data source manager import',
    'data_source_manager::get_data_source' => 'Data source loading',
    'data_source_manager::get_table_info' => 'Table info loading',
    'insertFieldPlaceholder' => 'Field insertion function',
    '@foreach($fields as $field)' => 'Server-side field rendering'
];

foreach ($component_features as $feature => $description) {
    if (strpos($field_browser_content, $feature) !== false) {
        echo "   ✓ $description found\n";
    } else {
        echo "   ✗ $description not found\n";
    }
}
echo "\n";

// Test 3: Check if template editor uses HTMX
echo "3. Testing HTMX integration...\n";
$template_content = file_get_contents('system/components/edges/email-template-editor.edge.php');

$htmx_features = [
    'hx-post=' => 'HTMX POST request',
    'hx-target=' => 'HTMX target element',
    'hx-trigger=' => 'HTMX trigger event',
    'hx-vals=' => 'HTMX values passing',
    'hx-indicator=' => 'HTMX loading indicator',
    '<x-data-source-field-browser' => 'Edge component usage',
    'id="field-browser-container"' => 'HTMX target container'
];

foreach ($htmx_features as $feature => $description) {
    if (strpos($template_content, $feature) !== false) {
        echo "   ✓ $description found\n";
    } else {
        echo "   ✗ $description not found\n";
    }
}
echo "\n";

// Test 4: Check if AJAX code was removed
echo "4. Testing AJAX removal...\n";
$ajax_patterns = [
    'fetch(' => 'AJAX fetch calls',
    'loadingFields = true' => 'Client-side loading state',
    'dataSourceFields = {}' => 'Client-side field storage',
    '.then(response => response.json())' => 'AJAX response handling'
];

$ajax_found = false;
foreach ($ajax_patterns as $pattern => $description) {
    if (strpos($template_content, $pattern) !== false) {
        echo "   ⚠ $description still found (should be removed)\n";
        $ajax_found = true;
    }
}

if (!$ajax_found) {
    echo "   ✓ All AJAX code successfully removed\n";
}
echo "\n";

// Test 5: Check API endpoint
echo "5. Testing API endpoint...\n";
$api_content = file_get_contents('system/api/email_campaigns.api.php');

if (strpos($api_content, 'function get_data_source_fields') !== false) {
    echo "   ✓ get_data_source_fields API endpoint exists\n";
} else {
    echo "   ✗ get_data_source_fields API endpoint not found\n";
}

if (strpos($api_content, 'Edge::render(\'data-source-field-browser\'') !== false) {
    echo "   ✓ API endpoint renders Edge component\n";
} else {
    echo "   ✗ API endpoint doesn't render Edge component\n";
}
echo "\n";

echo "✅ Server-side implementation verification completed!\n\n";

echo "Architecture Overview:\n";
echo "======================\n";
echo "1. 📄 Template Editor (email-template-editor.edge.php)\n";
echo "   - Loads data sources server-side using PHP\n";
echo "   - Uses HTMX for dynamic field loading\n";
echo "   - Minimal JavaScript, mostly server-side rendering\n\n";

echo "2. 🧩 Field Browser Component (data-source-field-browser.edge.php)\n";
echo "   - Standalone Edge component for field display\n";
echo "   - Server-side field processing and rendering\n";
echo "   - Handles both configured sources and table fallbacks\n\n";

echo "3. 🔌 API Endpoint (get_data_source_fields)\n";
echo "   - Returns rendered Edge component HTML\n";
echo "   - Server-side field processing\n";
echo "   - Clean separation of concerns\n\n";

echo "4. 🌐 HTMX Integration\n";
echo "   - Dynamic content loading without complex JavaScript\n";
echo "   - Server-side rendering with client-side updates\n";
echo "   - Progressive enhancement approach\n\n";

echo "Benefits:\n";
echo "=========\n";
echo "✓ Server-side rendering for better performance\n";
echo "✓ Consistent with system architecture\n";
echo "✓ Easier to maintain and debug\n";
echo "✓ Better SEO and accessibility\n";
echo "✓ Reduced JavaScript complexity\n";
echo "✓ Reusable Edge component\n\n";

echo "How it works:\n";
echo "=============\n";
echo "1. User opens template editor → Data sources loaded server-side\n";
echo "2. User selects data source → HTMX sends request to API\n";
echo "3. API renders field browser component → Returns HTML\n";
echo "4. HTMX updates target container → Fields appear instantly\n";
echo "5. User clicks field button → JavaScript inserts placeholder\n\n";
?>
