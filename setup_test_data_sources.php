<?php
/**
 * Setup script to create test data sources for the email template editor
 */

require_once 'system/startup_sequence_minimal.php';

use system\data_source_manager;
use system\database;

echo "Setting up test data sources for email template editor...\n\n";

try {
    // Check if data sources table exists
    $table_exists = false;
    try {
        $count = database::table('autobooks_data_sources')->count();
        $table_exists = true;
        echo "✓ Data sources table exists with $count existing data sources\n";
    } catch (Exception $e) {
        echo "⚠ Data sources table doesn't exist, creating it...\n";
    }

    // Create table if it doesn't exist
    if (!$table_exists) {
        $create_sql = "
        CREATE TABLE `autobooks_data_sources` (
            `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
            `name` varchar(255) NOT NULL COMMENT 'Display name for the data source',
            `table_name` varchar(255) NOT NULL COMMENT 'Database table name',
            `description` text DEFAULT NULL COMMENT 'Optional description',
            `category` varchar(50) NOT NULL DEFAULT 'other' COMMENT 'Data source category',
            `column_mapping` longtext DEFAULT NULL COMMENT 'JSON column mapping configuration',
            `filters` longtext DEFAULT NULL COMMENT 'JSON filter configuration',
            `status` varchar(20) NOT NULL DEFAULT 'active' COMMENT 'Data source status',
            `created_by` int(11) DEFAULT NULL COMMENT 'User ID who created this data source',
            `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
            `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
            PRIMARY KEY (`id`),
            KEY `idx_status_category` (`status`,`category`),
            KEY `idx_table_name` (`table_name`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Data source configurations for tables and email campaigns'
        ";
        
        database::rawQuery($create_sql);
        echo "✓ Data sources table created successfully\n";
    }

    // Get available tables to create data sources from
    $available_tables = data_source_manager::get_available_tables();
    echo "Found " . count($available_tables) . " available tables\n\n";

    // Create sample data sources
    $sample_sources = [
        [
            'name' => 'User Management Data',
            'table_name' => 'autobooks_users',
            'description' => 'System users and their information for email personalization',
            'category' => 'users',
            'status' => 'active'
        ],
        [
            'name' => 'Email Campaign Data',
            'table_name' => 'email_campaigns',
            'description' => 'Email campaign configurations and metadata',
            'category' => 'email',
            'status' => 'active'
        ],
        [
            'name' => 'Navigation Data',
            'table_name' => 'autobooks_navigation',
            'description' => 'System navigation menu items',
            'category' => 'system',
            'status' => 'active'
        ]
    ];

    // Add data sources that correspond to existing tables
    foreach ($sample_sources as $source) {
        // Check if table exists
        $table_exists = false;
        foreach ($available_tables as $table) {
            if ($table['name'] === $source['table_name']) {
                $table_exists = true;
                break;
            }
        }

        if ($table_exists) {
            // Check if data source already exists
            $existing = database::table('autobooks_data_sources')
                ->where('table_name', $source['table_name'])
                ->first();

            if (!$existing) {
                $source['created_at'] = date('Y-m-d H:i:s');
                $source['updated_at'] = date('Y-m-d H:i:s');
                $id = database::table('autobooks_data_sources')->insert($source);
                echo "✓ Created data source: {$source['name']} (ID: $id)\n";
            } else {
                echo "- Data source already exists: {$source['name']}\n";
            }
        } else {
            echo "⚠ Table {$source['table_name']} not found, skipping data source creation\n";
        }
    }

    echo "\n";

    // Show final count
    $final_count = database::table('autobooks_data_sources')->count();
    echo "✅ Setup complete! Total data sources: $final_count\n\n";

    echo "Next steps:\n";
    echo "1. Open the email template editor\n";
    echo "2. The data source dropdown should now show available data sources\n";
    echo "3. Select a data source to see available fields\n";
    echo "4. Click field buttons to insert placeholders\n\n";

    echo "If you still see an empty dropdown, the system will fall back to showing available tables.\n";

} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}
?>
