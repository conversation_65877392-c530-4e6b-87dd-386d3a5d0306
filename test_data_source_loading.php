<?php
/**
 * Test script to verify data source loading in template editor
 */

echo "Testing Data Source Loading Fix\n";
echo "===============================\n\n";

// Test 1: Check if files have correct syntax
echo "1. Testing file syntax...\n";
$output = shell_exec("php -l system/components/edges/email-template-editor.edge.php 2>&1");
if (strpos($output, 'No syntax errors') !== false) {
    echo "   ✓ email-template-editor.edge.php - syntax OK\n";
} else {
    echo "   ✗ email-template-editor.edge.php - syntax error: $output\n";
}
echo "\n";

// Test 2: Check if server-side data loading is implemented
echo "2. Testing server-side data loading implementation...\n";
$template_content = file_get_contents('system/components/edges/email-template-editor.edge.php');

$server_side_features = [
    'use system\data_source_manager' => 'Data source manager import',
    'data_source_manager::get_data_sources()' => 'Server-side data source loading',
    '@json($dropdown_sources)' => 'Server-side data passed to JavaScript',
    '@json($using_configured_sources)' => 'Configuration status passed to JavaScript',
    '@foreach($dropdown_sources as $source)' => 'Server-side dropdown population',
    '@if(!empty($dropdown_sources))' => 'Server-side conditional rendering'
];

foreach ($server_side_features as $feature => $description) {
    if (strpos($template_content, $feature) !== false) {
        echo "   ✓ $description found\n";
    } else {
        echo "   ✗ $description not found\n";
    }
}
echo "\n";

// Test 3: Check if AJAX calls were removed
echo "3. Testing AJAX removal...\n";
$ajax_features = [
    'fetch(' => 'AJAX calls',
    'loadingDataSources = true' => 'Loading state management',
    'get_data_sources' => 'API endpoint calls'
];

$ajax_found = false;
foreach ($ajax_features as $feature => $description) {
    if (strpos($template_content, $feature) !== false) {
        echo "   ⚠ $description still found (should be removed)\n";
        $ajax_found = true;
    }
}

if (!$ajax_found) {
    echo "   ✓ All AJAX calls successfully removed\n";
}
echo "\n";

// Test 4: Check status message implementation
echo "4. Testing status messages...\n";
$status_features = [
    '@if(empty($dropdown_sources))' => 'Empty data sources check',
    '@elseif(!$using_configured_sources)' => 'Table fallback message',
    '@else' => 'Configured sources success message',
    'Using Configured Data Sources' => 'Success message text'
];

foreach ($status_features as $feature => $description) {
    if (strpos($template_content, $feature) !== false) {
        echo "   ✓ $description found\n";
    } else {
        echo "   ✗ $description not found\n";
    }
}
echo "\n";

echo "✅ Data source loading fix verification completed!\n\n";

echo "What was fixed:\n";
echo "===============\n";
echo "1. ❌ BEFORE: Template editor used AJAX to load data sources (failed)\n";
echo "2. ✅ AFTER: Template editor uses server-side PHP to load data sources (works)\n";
echo "3. ✅ Uses same approach as working campaign form data source selector\n";
echo "4. ✅ Proper fallback from configured sources to available tables\n";
echo "5. ✅ Server-side status messages instead of client-side detection\n";
echo "6. ✅ No more empty dropdown - data is loaded before page renders\n\n";

echo "Expected behavior:\n";
echo "==================\n";
echo "1. Open email template editor\n";
echo "2. Data source dropdown should show: 'Autodesk_autorenew (autodesk_subscriptions)'\n";
echo "3. Green status message: 'Using Configured Data Sources'\n";
echo "4. Select the data source to see available fields\n";
echo "5. Click field buttons to insert placeholders\n\n";

echo "The data source from your database dump should now appear:\n";
echo "- ID: 1\n";
echo "- Name: Autodesk_autorenew\n";
echo "- Table: autodesk_subscriptions\n";
echo "- Status: active\n\n";
?>
