<?php
/**
 * Debug script to check data source configuration
 */

// Simple debug without full bootstrap
echo "Debug Data Source Configuration\n";
echo "===============================\n\n";

// Check if we can access the database directly
try {
    // Try to connect to database using PDO
    $host = 'localhost'; // Adjust as needed
    $dbname = 'autobooks'; // Adjust as needed
    
    // You may need to adjust these connection details
    echo "Attempting to connect to database...\n";
    
    // For now, let's just check if the file exists and show what we can
    echo "1. Checking data source component file...\n";
    if (file_exists('system/components/edges/data-source-field-browser.edge.php')) {
        echo "   ✓ Component file exists\n";
        
        $content = file_get_contents('system/components/edges/data-source-field-browser.edge.php');
        
        // Check for debug statements
        if (strpos($content, 'error_log("DEBUG:') !== false) {
            echo "   ✓ Debug logging enabled\n";
        } else {
            echo "   ✗ Debug logging not found\n";
        }
        
        // Check for JSON decoding
        if (strpos($content, 'json_decode($selected_columns, true)') !== false) {
            echo "   ✓ JSON decoding implemented\n";
        } else {
            echo "   ✗ JSON decoding not found\n";
        }
        
    } else {
        echo "   ✗ Component file not found\n";
    }
    echo "\n";
    
    echo "2. Expected data structure from your database dump:\n";
    echo "   Data Source ID: 1\n";
    echo "   Name: Autodesk_autorenew\n";
    echo "   Table: autodesk_subscriptions\n";
    echo "   Selected Columns (JSON):\n";
    
    $expected_json = '{
        "autodesk_subscriptions": [
            "subscriptionId",
            "subscriptionReferenceNumber", 
            "quantity",
            "status",
            "startDate",
            "endDate",
            "paymentMethod",
            "offeringId",
            "offeringCode",
            "offeringName",
            "autoRenew"
        ],
        "autodesk_accounts": [
            "name",
            "first_name",
            "last_name",
            "email",
            "city",
            "postal_code"
        ]
    }';
    
    echo "   " . $expected_json . "\n\n";
    
    echo "3. Testing JSON decoding:\n";
    $test_decode = json_decode($expected_json, true);
    if ($test_decode && is_array($test_decode)) {
        echo "   ✓ JSON decodes successfully\n";
        echo "   ✓ Found " . count($test_decode) . " tables\n";
        
        $total_fields = 0;
        foreach ($test_decode as $table => $columns) {
            echo "   ✓ Table '$table': " . count($columns) . " columns\n";
            $total_fields += count($columns);
        }
        echo "   ✓ Total expected fields: $total_fields\n";
    } else {
        echo "   ✗ JSON decode failed\n";
    }
    echo "\n";
    
    echo "4. Debugging steps:\n";
    echo "   1. Try to access the email template editor\n";
    echo "   2. Select the 'Autodesk_autorenew' data source\n";
    echo "   3. Check the server error logs for DEBUG messages\n";
    echo "   4. Look for lines starting with 'DEBUG: Data source retrieved:'\n";
    echo "   5. Look for lines starting with 'DEBUG: Raw selected_columns:'\n";
    echo "   6. Look for lines starting with 'DEBUG: Decoded selected_columns:'\n\n";
    
    echo "5. Common issues to check:\n";
    echo "   - Is data_source_manager::get_data_source() returning the data?\n";
    echo "   - Is the selected_columns field being decoded from JSON?\n";
    echo "   - Are the column arrays properly structured?\n";
    echo "   - Is the component receiving the correct data_source_id?\n\n";
    
    echo "6. Error log locations to check:\n";
    echo "   - /var/log/apache2/error.log\n";
    echo "   - /var/log/php_errors.log\n";
    echo "   - Your hosting provider's error logs\n\n";
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}

echo "Debug script completed. Check the error logs after trying to use the data source selector.\n";
?>
