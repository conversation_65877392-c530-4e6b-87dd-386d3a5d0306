@props([
    'mode' => 'new', // 'new' or 'edit'
    'template' => '',
    'campaign_id' => null,
    'content' => '',
    'template_name' => ''
])

@php
// Load template content if editing
if ($mode === 'edit' && $template) {
    $template_path = FS_APP_ROOT . '/resources/email_templates/' . $template;
    if (file_exists($template_path)) {
        $content = file_get_contents($template_path);
        $template_name = pathinfo($template, PATHINFO_FILENAME);
        $template_name = str_replace('.emlt', '', $template_name);
    }
}
@endphp

<div class="space-y-6">
    <!-- Header -->
    <div class="border-b border-gray-200 pb-4">
        <h2 class="text-lg font-medium text-gray-900">
            {{ $mode === 'edit' ? 'Edit Template: ' . $template_name : 'Create New Email Template' }}
        </h2>
        <p class="mt-1 text-sm text-gray-600">
            {{ $mode === 'edit' ? 'Modify the existing email template.' : 'Create a new email template with placeholders.' }}
        </p>
    </div>

    <!-- Template Form -->
    <form hx-post="{{ APP_ROOT }}/api/email_campaigns/save_template"
          hx-target="#campaign_modal_body"
          hx-swap="innerHTML"
          class="space-y-6">
        
        <input type="hidden" name="mode" value="{{ $mode }}">
        <input type="hidden" name="original_template" value="{{ $template }}">
        @if($campaign_id)
            <input type="hidden" name="campaign_id" value="{{ $campaign_id }}">
        @endif

        <!-- Template Name -->
        <div>
            <label for="template_name" class="block text-sm font-medium text-gray-700">Template Name</label>
            <input type="text" 
                   name="template_name" 
                   id="template_name"
                   value="{{ $template_name }}"
                   required
                   placeholder="Enter template name (e.g., subscription_renewal)"
                   class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm">
            <p class="mt-1 text-sm text-gray-500">
                Use lowercase letters, numbers, and underscores only. Will be saved as .emlt.php file.
            </p>
        </div>

        <!-- Template Content -->
        <div x-data="{
                editor: null,
                placeholders: {},
                loadingPlaceholders: false
             }"
             x-init="
                // Load placeholders if campaign_id is available
                @if($campaign_id)
                    loadingPlaceholders = true;
                    fetch('{{ APP_ROOT }}/api/email_campaigns/get_campaign_placeholders', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/x-www-form-urlencoded',
                        },
                        body: 'campaign_id={{ $campaign_id }}'
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            placeholders = data.placeholders;
                        } else {
                            console.error('Error loading placeholders:', data.message);
                            // Use default placeholders
                            placeholders = {
                                'endCustomerName': 'End Customer Name',
                                'product_name': 'Product Name',
                                'term': 'Term',
                                'seats': 'Seats',
                                'subscriptionReferenceNumber': 'Subscription Reference Number',
                                'opportunityNumber': 'Opportunity Number',
                                'endCustomerCsn': 'End Customer CSN',
                                'status': 'Status',
                                'startDate': 'Start Date',
                                'endDate': 'End Date'
                            };
                        }
                        loadingPlaceholders = false;
                        initializeEditor();
                    })
                    .catch(error => {
                        console.error('Error loading placeholders:', error);
                        loadingPlaceholders = false;
                        // Use default placeholders
                        placeholders = {
                            'endCustomerName': 'End Customer Name',
                            'product_name': 'Product Name',
                            'term': 'Term',
                            'seats': 'Seats',
                            'subscriptionReferenceNumber': 'Subscription Reference Number',
                            'opportunityNumber': 'Opportunity Number',
                            'endCustomerCsn': 'End Customer CSN',
                            'status': 'Status',
                            'startDate': 'Start Date',
                            'endDate': 'End Date'
                        };
                        initializeEditor();
                    });
                @else
                    // Use default placeholders when no campaign_id
                    placeholders = {
                        'endCustomerName': 'End Customer Name',
                        'product_name': 'Product Name',
                        'term': 'Term',
                        'seats': 'Seats',
                        'subscriptionReferenceNumber': 'Subscription Reference Number',
                        'opportunityNumber': 'Opportunity Number',
                        'endCustomerCsn': 'End Customer CSN',
                        'status': 'Status',
                        'startDate': 'Start Date',
                        'endDate': 'End Date'
                    };
                    $nextTick(() => initializeEditor());
                @endif

                function initializeEditor() {
                    if (typeof Jodit !== 'undefined' && Object.keys(placeholders).length > 0) {
                        // Build placeholder list dynamically
                        const placeholderList = {};
                        Object.keys(placeholders).forEach(key => {
                            placeholderList['{{' + key + '}}'] = placeholders[key];
                        });

                        // Configure placeholder dropdown
                        Jodit.defaultOptions.controls.insertPlaceholder = {
                            tooltip: 'Insert Placeholder',
                            icon: 'plus',
                            list: placeholderList,
                            exec(editor, _, { control }) {
                                const value = control.args && control.args[0];
                                if (value) {
                                    editor.s.insertHTML(value);
                                }
                            }
                        };

                        // Initialize editor
                        editor = Jodit.make('#template_content', {
                            height: 400,
                            buttons: [...Jodit.defaultOptions.buttons, 'insertPlaceholder'],
                            placeholder: 'Enter your email template content here...',
                            toolbarAdaptive: false,
                            showCharsCounter: false,
                            showWordsCounter: false,
                            showXPathInStatusbar: false
                        });
                    }
                }
             "
             @submit.window="if (editor) { document.getElementById('template_content').value = editor.getEditorValue(); }">
            <label for="template_content" class="block text-sm font-medium text-gray-700 mb-2">Template Content</label>
            <textarea name="template_content"
                      id="template_content"
                      rows="20"
                      class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm">{{ htmlspecialchars($content) }}</textarea>
        </div>

        <!-- Placeholder Help -->
        <div class="bg-blue-50 border border-blue-200 rounded-md p-4">
            <div class="flex">
                <div class="flex-shrink-0">
                    <svg class="h-5 w-5 text-blue-400" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
                    </svg>
                </div>
                <div class="ml-3">
                    <h3 class="text-sm font-medium text-blue-800">Available Placeholders</h3>
                    <div class="mt-2 text-sm text-blue-700">
                        <div x-show="loadingPlaceholders" class="flex items-center">
                            <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-500 mr-2"></div>
                            <span>Loading placeholders...</span>
                        </div>
                        <div x-show="!loadingPlaceholders">
                            <p class="mb-2">Use these placeholders in your template:</p>
                            <div class="grid grid-cols-2 gap-2 text-xs">
                                <template x-for="(displayName, placeholder) in placeholders" :key="placeholder">
                                    <div>
                                        <code x-text="'{{' + placeholder + '}}'"></code> -
                                        <span x-text="displayName"></span>
                                    </div>
                                </template>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Form Actions -->
        <div class="border-t border-gray-200 pt-6">
            <div class="flex justify-end space-x-3">
                <button type="button"
                        @click="showCampaignModal = false"
                        class="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                    Cancel
                </button>

                <button type="submit"
                        class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                    <svg class="-ml-1 mr-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-3m-1 4l-3-3m0 0l-3 3m3-3v12"></path>
                    </svg>
                    {{ $mode === 'edit' ? 'Update Template' : 'Create Template' }}
                </button>
            </div>
        </div>
    </form>
</div>




