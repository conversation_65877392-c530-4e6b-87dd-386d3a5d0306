@props([
    'data_source_id' => null,
    'show_empty_message' => true
])

@php
use system\data_source_manager;

$fields = [];
$data_source_name = '';
$table_name = '';

if ($data_source_id) {
    try {
        // Check if this is a table fallback (starts with 'table:')
        if (str_starts_with($data_source_id, 'table:')) {
            $table_name = str_replace('table:', '', $data_source_id);
            $data_source_name = $table_name;

            // Get table info directly
            $table_info = data_source_manager::get_table_info($table_name);
            if ($table_info && isset($table_info['columns'])) {
                foreach ($table_info['columns'] as $column) {
                    // Skip system columns
                    if (!in_array($column['Field'], ['id', 'created_at', 'updated_at', 'deleted_at'])) {
                        $display_name = ucwords(str_replace(['_', '-'], ' ', $column['Field']));
                        $type = 'other';

                        if (stripos($column['Type'], 'varchar') !== false || stripos($column['Type'], 'text') !== false) {
                            $type = 'text';
                        } elseif (stripos($column['Type'], 'int') !== false) {
                            $type = 'number';
                        } elseif (stripos($column['Type'], 'date') !== false || stripos($column['Type'], 'time') !== false) {
                            $type = 'date';
                        }

                        $fields[] = [
                            'name' => $column['Field'],
                            'display' => $display_name,
                            'type' => $type,
                            'db_type' => $column['Type']
                        ];
                    }
                }
            }
        } else {
            // Regular configured data source
            $data_source = data_source_manager::get_data_source((int)$data_source_id);
            if ($data_source) {
                $data_source_name = $data_source['name'];
                $table_name = $data_source['table_name'];

                // Check if data source has selected columns configuration
                if (!empty($data_source['selected_columns']) && is_array($data_source['selected_columns'])) {
                    // Use configured selected columns
                    foreach ($data_source['selected_columns'] as $table => $columns) {
                        if (is_array($columns)) {
                            foreach ($columns as $column_name) {
                                // Skip system columns
                                if (!in_array($column_name, ['id', 'created_at', 'updated_at', 'deleted_at'])) {
                                    $display_name = ucwords(str_replace(['_', '-'], ' ', $column_name));

                                    // Try to determine type from column name patterns
                                    $type = 'text'; // default
                                    if (stripos($column_name, 'id') !== false ||
                                        stripos($column_name, 'count') !== false ||
                                        stripos($column_name, 'quantity') !== false ||
                                        stripos($column_name, 'number') !== false) {
                                        $type = 'number';
                                    } elseif (stripos($column_name, 'date') !== false ||
                                              stripos($column_name, 'time') !== false) {
                                        $type = 'date';
                                    }

                                    $fields[] = [
                                        'name' => $column_name,
                                        'display' => $display_name,
                                        'type' => $type,
                                        'db_type' => 'configured',
                                        'table' => $table
                                    ];
                                }
                            }
                        }
                    }
                } else {
                    // Fallback to table info if no selected columns configured
                    $table_info = data_source_manager::get_table_info($data_source['table_name']);
                    if ($table_info && isset($table_info['columns'])) {
                        foreach ($table_info['columns'] as $column) {
                            // Skip system columns
                            if (!in_array($column['Field'], ['id', 'created_at', 'updated_at', 'deleted_at'])) {
                                $display_name = ucwords(str_replace(['_', '-'], ' ', $column['Field']));
                                $type = 'other';

                                if (stripos($column['Type'], 'varchar') !== false || stripos($column['Type'], 'text') !== false) {
                                    $type = 'text';
                                } elseif (stripos($column['Type'], 'int') !== false) {
                                    $type = 'number';
                                } elseif (stripos($column['Type'], 'date') !== false || stripos($column['Type'], 'time') !== false) {
                                    $type = 'date';
                                }

                                $fields[] = [
                                    'name' => $column['Field'],
                                    'display' => $display_name,
                                    'type' => $type,
                                    'db_type' => $column['Type']
                                ];
                            }
                        }
                    }
                }
            }
        }
    } catch (Exception $e) {
        // Log error but don't break the UI
        error_log("Error loading data source fields: " . $e->getMessage());
    }
}
@endphp

@if($data_source_id && !empty($fields))
    <div class="space-y-2">
        <div class="flex items-center justify-between">
            <label class="block text-sm font-medium text-gray-700">
                Available Fields
                @if($data_source_name)
                    <span class="text-gray-500 font-normal">({{ $data_source_name }})</span>
                @endif
            </label>
            <span class="text-xs text-gray-500">{{ count($fields) }} fields</span>
        </div>
        
        <div class="grid grid-cols-2 gap-2 max-h-40 overflow-y-auto border border-gray-200 rounded p-2 bg-white">
            @foreach($fields as $field)
                <button type="button"
                        onclick="insertFieldPlaceholder('{{ $field['name'] }}')"
                        class="text-left px-2 py-1 text-xs bg-gray-50 hover:bg-indigo-50 border border-gray-200 rounded transition-colors duration-150"
                        title="Insert @{{ {{ $field['name'] }} @}} - {{ $field['db_type'] }}{{ isset($field['table']) ? ' from ' . $field['table'] : '' }}">
                    <div class="flex items-center justify-between">
                        <div class="font-medium text-gray-900">
                            {{ $field['display'] }}
                            @if(isset($field['table']) && count($fields) > 10)
                                <span class="text-xs text-gray-400 ml-1">({{ $field['table'] }})</span>
                            @endif
                        </div>
                        <div class="text-xs px-1 rounded
                                    @if($field['type'] === 'text') bg-blue-100 text-blue-600
                                    @elseif($field['type'] === 'number') bg-green-100 text-green-600
                                    @elseif($field['type'] === 'date') bg-purple-100 text-purple-600
                                    @else bg-gray-100 text-gray-600
                                    @endif">
                            {{ $field['type'] }}
                        </div>
                    </div>
                    <div class="text-gray-500">{{{{ $field['name'] }}}}</div>
                </button>
            @endforeach
        </div>
    </div>
@elseif($data_source_id && empty($fields))
    <div class="text-sm text-gray-500 italic p-4 text-center border border-gray-200 rounded bg-gray-50">
        No fields available for this data source.
    </div>
@elseif($show_empty_message)
    <div class="text-sm text-gray-500 italic p-4 text-center border border-gray-200 rounded bg-gray-50">
        Select a data source to view available fields.
    </div>
@endif

<script>
function insertFieldPlaceholder(fieldName) {
    const placeholder = '&#123;&#123;' + fieldName + '&#125;&#125;';
    
    // Try to insert into Jodit editor first
    if (window.joditEditor && window.joditEditor.s) {
        window.joditEditor.s.insertHTML(placeholder);
        window.joditEditor.focus();
        return;
    }
    
    // Fallback to textarea
    const editor = document.querySelector('#template_content');
    if (editor) {
        const start = editor.selectionStart;
        const end = editor.selectionEnd;
        const text = editor.value;
        editor.value = text.substring(0, start) + placeholder + text.substring(end);
        editor.selectionStart = editor.selectionEnd = start + placeholder.length;
        editor.focus();
    }
}
</script>
